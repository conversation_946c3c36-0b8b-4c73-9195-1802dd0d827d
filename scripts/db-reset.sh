#!/bin/bash

# Database reset script - WARNING: This will delete all data!
set -e

# Source utility functions
source "$(dirname "$0")/utils.sh"

echo "🔄 Resetting database (this will delete all data)..."
echo "⚠️  This action cannot be undone!"

# Prompt for confirmation
read -p "Are you sure you want to reset the database? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Database reset cancelled."
    exit 1
fi

# Pre-flight checks
check_docker
check_env_file

# Stop containers and remove volumes
echo "🗑️  Removing containers and volumes..."
docker compose down -v

# Start containers
echo "📦 Starting containers..."
docker compose up -d --build

# Wait for database and setup
if wait_for_database; then
    run_migrations && seed_database
    echo "🎉 Database reset and seeded complete!"
    echo "📊 Database: mysql://root:rootpassword@localhost:3306/trading_app_dev"
    echo "🔍 View database: npx prisma studio"
else
    echo "❌ Failed to reset database. Check logs with: make logs"
    exit 1
fi
