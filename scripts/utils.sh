#!/bin/bash

# Utility functions for trading app scripts

# Check if <PERSON><PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Check if .env file exists and create from example if not
check_env_file() {
    if [ ! -f .env ]; then
        echo "⚠️  .env file not found. Copying from .env.example..."
        if [ -f .env.example ]; then
            cp .env.example .env
            echo "✅ Created .env file from .env.example"
        else
            echo "❌ .env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
}

# Wait for database to be ready
wait_for_database() {
    echo "⏳ Waiting for database to be ready..."
    timeout=60
    counter=0

    while ! docker compose exec -T db mysqladmin ping -h localhost -u root -prootpassword --silent 2>/dev/null; do
        if [ $counter -eq $timeout ]; then
            echo "❌ Database failed to start within $timeout seconds"
            echo "📋 Check logs with: make logs"
            return 1
        fi
        echo "Waiting for MySQL... ($counter/$timeout)"
        sleep 2
        counter=$((counter + 1))
    done

    echo "✅ Database is ready!"
    return 0
}

# Run Prisma migrations
run_migrations() {
    echo "🔄 Running Prisma migrations..."
    if npx prisma migrate deploy; then
        echo "✅ Migrations completed successfully!"
        return 0
    else
        echo "❌ Migration failed!"
        return 1
    fi
}

# Seed the database
seed_database() {
    echo "🌱 Seeding database..."
    if npm run db:seed; then
        echo "✅ Database seeded successfully!"
        return 0
    else
        echo "❌ Database seeding failed!"
        return 1
    fi
}

# Display success message with useful information
show_success_message() {
    echo "🎉 Setup complete! Your trading app is running!"
    echo "📊 Database: mysql://root:rootpassword@localhost:3306/trading_app_dev"
    echo "🔍 View database: npx prisma studio"
    echo "📋 View logs: make logs"
    echo "🛑 Stop app: make down"
}
