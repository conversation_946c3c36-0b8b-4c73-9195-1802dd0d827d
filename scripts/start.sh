#!/bin/bash

# Start script for trading app
set -e

# Source utility functions
source "$(dirname "$0")/utils.sh"

echo "🚀 Starting trading app with database..."

# Pre-flight checks
check_docker
check_env_file

# Start containers
echo "📦 Starting containers..."
docker compose up -d --build

# Wait for database and setup
if wait_for_database; then
    run_migrations && seed_database && show_success_message
else
    echo "❌ Failed to start database. Check logs with: make logs"
    exit 1
fi
