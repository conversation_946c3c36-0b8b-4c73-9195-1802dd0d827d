#!/bin/bash

# Database initialization script
set -e

# Source utility functions
source "$(dirname "$0")/utils.sh"

echo "🔧 Initializing database only..."

# Pre-flight checks
check_docker
check_env_file

# Start the database container only
echo "📦 Starting MySQL container..."
docker compose up -d db

# Wait for database and setup
if wait_for_database; then
    run_migrations && seed_database
    echo "🎉 Database initialization complete!"
    echo "📊 You can view your database with: npx prisma studio"
else
    echo "❌ Failed to initialize database. Check logs with: make logs"
    exit 1
fi
