#!/bin/bash

# Database initialization script
set -e

echo "🚀 Starting database initialization..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start the database container
echo "📦 Starting MySQL container..."
docker compose -f 'docker-compose.yml' up -d db

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
timeout=60
counter=0

while ! docker compose -f 'docker-compose.yml' exec -T db mysqladmin ping -h localhost -u root -prootpassword --silent; do
    if [ $counter -eq $timeout ]; then
        echo "❌ Database failed to start within $timeout seconds"
        exit 1
    fi
    echo "Waiting for MySQL... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 1))
done

echo "✅ Database is ready!"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
fi

# Run Prisma migrations
echo "🔄 Running Prisma migrations..."
npx prisma migrate deploy

# Run database seed
echo "🌱 Seeding database..."
npm run db:seed

echo "🎉 Database initialization complete!"
echo "📊 You can view your database with: npx prisma studio"
