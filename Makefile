up:
	./scripts/start.sh

down:
	./scripts/stop.sh

db-reset:
	./scripts/db-reset.sh

db-init:
	./scripts/init-db.sh

logs:
	docker compose logs -f

status:
	docker compose ps

help:
	@echo "Available commands:"
	@echo "  make up       - Start the trading app with database setup"
	@echo "  make down     - Stop the trading app"
	@echo "  make db-reset - Reset database (deletes all data)"
	@echo "  make db-init  - Initialize database only"
	@echo "  make logs     - View container logs"
	@echo "  make status   - Show container status"
	@echo "  make help     - Show this help message"