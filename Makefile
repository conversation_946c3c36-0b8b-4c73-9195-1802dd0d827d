up:
	@echo "🚀 Starting trading app with database..."
	docker compose -f 'docker-compose.yml' up -d --build
	@echo "⏳ Waiting for database to be ready..."
	@timeout=60; counter=0; \
	while ! docker compose -f 'docker-compose.yml' exec -T db mysqladmin ping -h localhost -u root -prootpassword --silent; do \
		if [ $$counter -eq $$timeout ]; then \
			echo "❌ Database failed to start within $$timeout seconds"; \
			exit 1; \
		fi; \
		echo "Waiting for MySQL... ($$counter/$$timeout)"; \
		sleep 2; \
		counter=$$((counter + 1)); \
	done
	@echo "✅ Database is ready! Running Prisma migrations..."
	npx prisma migrate deploy
	@echo "🌱 Seeding database..."
	npm run db:seed
	@echo "🎉 Setup complete! Your trading app is running!"
	@echo "📊 Database: mysql://root:rootpassword@localhost:3306/trading_app_dev"
	@echo "🔍 View database: npx prisma studio"

down:
	@echo "🛑 Stopping trading app..."
	docker compose -f 'docker-compose.yml' down
	@echo "✅ Stopped!"

db-reset:
	@echo "🔄 Resetting database (this will delete all data)..."
	docker compose -f 'docker-compose.yml' down -v
	docker compose -f 'docker-compose.yml' up -d --build
	@echo "⏳ Waiting for database to be ready..."
	@timeout=60; counter=0; \
	while ! docker compose -f 'docker-compose.yml' exec -T db mysqladmin ping -h localhost -u root -prootpassword --silent; do \
		if [ $$counter -eq $$timeout ]; then \
			echo "❌ Database failed to start within $$timeout seconds"; \
			exit 1; \
		fi; \
		echo "Waiting for MySQL... ($$counter/$$timeout)"; \
		sleep 2; \
		counter=$$((counter + 1)); \
	done
	@echo "✅ Database is ready! Running Prisma migrations..."
	npx prisma migrate deploy
	@echo "🌱 Seeding database..."
	npm run db:seed
	@echo "🎉 Database reset and seeded complete!"

db-init:
	@echo "🔧 Initializing database only..."
	./scripts/init-db.sh

logs:
	docker compose -f 'docker-compose.yml' logs -f

status:
	docker compose -f 'docker-compose.yml' ps

help:
	@echo "Available commands:"
	@echo "  make up       - Start the trading app with database setup"
	@echo "  make down     - Stop the trading app"
	@echo "  make db-reset - Reset database (deletes all data)"
	@echo "  make db-init  - Initialize database only"
	@echo "  make logs     - View container logs"
	@echo "  make status   - Show container status"
	@echo "  make help     - Show this help message"